class AccessControl {
    constructor() {
        this.form = document.getElementById('accessForm');
        this.passwordInput = document.getElementById('password');
        this.submitBtn = document.getElementById('submitBtn');
        this.btnText = document.querySelector('.btn-text');
        this.btnLoading = document.querySelector('.btn-loading');
        this.errorMessage = document.getElementById('errorMessage');
        this.successMessage = document.getElementById('successMessage');
        
        this.bindEvents();
        this.checkAccessStatus();
    }

    bindEvents() {
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        this.passwordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSubmit(e);
            }
        });
        
        // Clear messages when user starts typing
        this.passwordInput.addEventListener('input', () => {
            this.hideMessages();
        });
    }

    async checkAccessStatus() {
        try {
            const response = await fetch('/api/access/status');
            const data = await response.json();
            
            if (data.hasAccess) {
                // User already has access, redirect to main app
                this.showSuccess('Access already granted. Redirecting...');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            }
        } catch (error) {
            console.log('Access status check failed:', error);
            // Continue with normal flow
        }
    }

    async handleSubmit(e) {
        e.preventDefault();
        
        const password = this.passwordInput.value.trim();
        
        if (!password) {
            this.showError('Please enter the access password');
            return;
        }

        this.setLoading(true);
        this.hideMessages();

        try {
            const response = await fetch('/api/access/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ password }),
                credentials: 'include' // Include cookies
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.showSuccess('Access granted! Redirecting to application...');
                
                // Clear the form
                this.passwordInput.value = '';
                
                // Redirect to main application after short delay
                setTimeout(() => {
                    window.location.href = '/';
                }, 1500);
            } else {
                this.showError(data.error || 'Invalid access password');
                this.passwordInput.focus();
                this.passwordInput.select();
            }
        } catch (error) {
            console.error('Access verification error:', error);
            this.showError('Connection error. Please try again.');
        } finally {
            this.setLoading(false);
        }
    }

    setLoading(loading) {
        this.submitBtn.disabled = loading;
        
        if (loading) {
            this.btnText.style.display = 'none';
            this.btnLoading.style.display = 'flex';
        } else {
            this.btnText.style.display = 'block';
            this.btnLoading.style.display = 'none';
        }
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        this.successMessage.style.display = 'none';
        
        // Add shake animation
        this.errorMessage.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            this.errorMessage.style.animation = '';
        }, 500);
    }

    showSuccess(message) {
        this.successMessage.textContent = message;
        this.successMessage.style.display = 'block';
        this.errorMessage.style.display = 'none';
    }

    hideMessages() {
        this.errorMessage.style.display = 'none';
        this.successMessage.style.display = 'none';
    }
}

// Add shake animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);

// Initialize access control when page loads
document.addEventListener('DOMContentLoaded', () => {
    new AccessControl();
});

// Focus password input when page loads
window.addEventListener('load', () => {
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.focus();
    }
});
