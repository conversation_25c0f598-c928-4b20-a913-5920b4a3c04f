/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background: #1a1a1a;
    min-height: 100vh;
    color: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    position: relative;
}

/* Header */
.header {
    padding: 60px 20px 20px 20px;
    text-align: left;
}

.header h1 {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header h1 i {
    color: #00d4aa;
    font-size: 1rem;
}

.subtitle {
    font-size: 0.9rem;
    color: #888;
    display: none; /* Hide subtitle in mobile design */
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
}

/* Burger Menu */
.burger-menu {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.burger-menu:hover {
    background: rgba(26, 26, 26, 0.9);
}

.burger-icon {
    width: 24px;
    height: 18px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.burger-icon span {
    width: 100%;
    height: 2px;
    background: #ffffff;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.burger-menu.active .burger-icon span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.burger-menu.active .burger-icon span:nth-child(2) {
    opacity: 0;
}

.burger-menu.active .burger-icon span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Side Menu */
.side-menu {
    position: fixed;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100vh;
    background: #1a1a1a;
    z-index: 1001;
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.side-menu.active {
    left: 0;
}

.side-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #333;
}

.side-menu-header h3 {
    color: #ffffff;
    margin: 0;
    font-size: 1.3rem;
}

.close-menu {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.8rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-menu:hover {
    background: #333;
}

.side-menu-content {
    padding: 20px 0;
}

.menu-item {
    width: 100%;
    padding: 15px 25px;
    background: none;
    border: none;
    color: #ffffff;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 1rem;
    transition: background 0.3s ease;
}

.menu-item:hover {
    background: #333;
}

.menu-item i {
    width: 20px;
    color: #00d4aa;
}

.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Voice Interface */
.voice-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: transparent;
}

.conversation-display {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
    background: transparent;
    margin-bottom: 20px;
}

.welcome-message {
    margin-bottom: 30px;
}

.welcome-message p {
    font-weight: 600;
    color: #ffffff;
    line-height: 1.3;
    margin-bottom: 15px;
}

.welcome-message i {
    display: none;
}

.message {
    margin-bottom: 20px;
    line-height: 1.4;
    word-wrap: break-word;
}

.user-message {
    color: #ffffff;
    text-align: left;
    font-weight: 500;
}

.ai-message {
    color: #ffffff;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 30px;
}

.play-audio-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #00d4aa;
    color: #1a1a1a;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.play-audio-btn:hover {
    background: #00b894;
    transform: scale(1.05);
}

.message-content {
    display: flex;
    flex-direction: column;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    margin: 15px auto;
    max-width: 90%;
}

.error-content strong {
    display: block;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.error-content details {
    margin-top: 10px;
    padding: 8px;
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

.error-content summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 5px;
}

.error-content pre {
    font-size: 0.85em;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 5px 0 0 0;
}

/* Voice Controls */
.voice-controls {
    position: fixed;
    bottom: 39px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    z-index: 1001;
}

.mic-button {
    width: 73px;
    height: 73px;
    border-radius: 50%;
    border: none;
    background: #333;
    color: #ffffff;
    font-size: 1.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.mic-button:hover {
    background: #444;
    transform: scale(1.05);
}

.mic-button.recording {
    background: #00d4aa;
    animation: pulse 1.5s infinite;
}

.mic-button.listening {
    background: #007acc;
    animation: listening-pulse 2s infinite;
}

.mic-button:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none !important;
}

.mic-button:disabled:hover {
    background: #666;
    transform: none;
}

.mic-status {
    display: none; /* Hide status text in mobile design */
}



@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes listening-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 122, 204, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 10px rgba(0, 122, 204, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 122, 204, 0);
    }
}

/* Stop Button */
.stop-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: #ff4757;
    color: #ffffff;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.stop-button:hover {
    background: #ff3742;
    transform: scale(1.05);
}

.stop-text {
    font-size: 0.7rem;
    margin-top: 2px;
    font-weight: 500;
}



.audio-controls {
    display: none; /* Hide audio controls in mobile design */
}

.control-btn {
    display: none;
}

/* Status Indicator */
.status-indicator {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    max-width: 280px;
    opacity: 1;
}

.status-indicator.hidden {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
    pointer-events: none;
}

.status-text {
    font-size: 0.8rem;
    color: #00d4aa;
    font-weight: 500;
}

/* Lead Panel - Hidden in mobile design */
.lead-panel {
    display: none;
}

/* Footer - Hidden in mobile design */
.footer {
    display: none;
}

/* Progress indicator */
.progress-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 212, 170, 0.2);
    color: #00d4aa;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: none;
}

.progress-indicator.show {
    display: block;
}

/* Responsive Typography - Mobile First */

/* Base mobile styles (320px - 480px) */
.welcome-message p {
    font-size: 1.5rem; /* Reduced from 1.8rem for small screens */
}

.ai-message {
    font-size: 1.5rem; /* Reduced from 1.8rem for small screens */
}

.message {
    font-size: 1rem; /* Reduced from 1.1rem for small screens */
}

.header h1 {
    font-size: 1rem; /* Reduced from 1.1rem for small screens */
}

/* Small mobile screens (up to 375px) */
@media (max-width: 375px) {
    .welcome-message p {
        font-size: 1.3rem;
        line-height: 1.2;
    }

    .ai-message {
        font-size: 1.3rem;
        line-height: 1.2;
        margin-bottom: 25px;
    }

    .message {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .header h1 {
        font-size: 0.95rem;
    }

    .subtitle {
        font-size: 0.8rem;
    }

    .status-text {
        font-size: 0.75rem;
    }

    .container {
        padding: 0 15px;
    }
}

/* Medium mobile screens (376px - 480px) */
@media (min-width: 376px) and (max-width: 480px) {
    .welcome-message p {
        font-size: 1.6rem;
    }

    .ai-message {
        font-size: 1.6rem;
    }

    .message {
        font-size: 1.05rem;
    }
}

/* Large mobile screens (481px - 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .welcome-message p {
        font-size: 1.8rem;
    }

    .ai-message {
        font-size: 1.8rem;
    }

    .message {
        font-size: 1.1rem;
    }

    .header h1 {
        font-size: 1.1rem;
    }

    .container {
        max-width: 450px;
    }
}

/* Tablet and larger screens (768px+) */
@media (min-width: 768px) {
    .container {
        max-width: 500px;
        padding: 0 30px;
    }

    .welcome-message p {
        font-size: 2rem;
        line-height: 1.3;
    }

    .ai-message {
        font-size: 2rem;
        line-height: 1.3;
        margin-bottom: 35px;
    }

    .message {
        font-size: 1.2rem;
        line-height: 1.4;
    }

    .header h1 {
        font-size: 1.2rem;
    }

    .subtitle {
        display: block; /* Show subtitle on larger screens */
        font-size: 1rem;
    }

    .mic-status {
        display: block; /* Show mic status on larger screens */
        font-size: 0.9rem;
        color: #888;
        margin-top: 5px;
    }

    .status-text {
        font-size: 0.9rem;
    }

    .conversation-display {
        padding: 30px 0;
    }
}

/* Large screens (1024px+) */
@media (min-width: 1024px) {
    .container {
        max-width: 600px;
    }

    .welcome-message p {
        font-size: 2.2rem;
    }

    .ai-message {
        font-size: 2.2rem;
    }

    .message {
        font-size: 1.3rem;
    }
}

/* AI Thinking Animation */
.ai-thinking-indicator {
    position: fixed;
    bottom: 39px;
    left: 129px;
    z-index: 1000;
    display: block;
    padding: 0;
    margin: 0;
    background: transparent;
    border: none;
    animation: fadeIn 0.3s ease-in-out;
    width: 73px;
    height: 73px;
}

/* Alternative: Bottom Left Corner Position */
.ai-thinking-indicator.bottom-left {
    bottom: 30px;
    left: 30px;
    transform: none;
}

.thinking-animation {
    width: 73px;
    height: 73px;
    position: relative;
}

.thinking-animation .blobs {
    width: 100%;
    height: 100%;
}

.thinking-animation svg {
    position: relative;
    height: 100%;
    z-index: 2;
}

.thinking-animation .blob {
    animation: rotate 25s infinite alternate ease-in-out;
    transform-origin: 50% 50%;
    opacity: 0.7;
}

.thinking-animation .blob path {
    animation: blob-anim-1 5s infinite alternate cubic-bezier(0.45, 0.2, 0.55, 0.8);
    transform-origin: 50% 50%;
    transform: scale(0.8);
    transition: fill 800ms ease;
}

.thinking-animation .blob.alt {
    animation-direction: alternate-reverse;
    opacity: 0.3;
}

.thinking-animation .blob-1 path {
    fill: #00d4aa;
    filter: blur(1rem);
}

.thinking-animation .blob-2 {
    animation-duration: 18s;
    animation-direction: alternate-reverse;
}

.thinking-animation .blob-2 path {
    fill: #4344ad;
    animation-name: blob-anim-2;
    animation-duration: 7s;
    filter: blur(0.75rem);
    transform: scale(0.78);
}

.thinking-animation .blob-2.alt {
    animation-direction: alternate;
}

.thinking-animation .blob-3 {
    animation-duration: 23s;
}

.thinking-animation .blob-3 path {
    fill: #74d9e1;
    animation-name: blob-anim-3;
    animation-duration: 6s;
    filter: blur(0.5rem);
    transform: scale(0.76);
}

.thinking-animation .blob-4 {
    animation-duration: 31s;
    animation-direction: alternate-reverse;
    opacity: 0.9;
}

.thinking-animation .blob-4 path {
    fill: #1a1a1a;
    animation-name: blob-anim-4;
    animation-duration: 10s;
    filter: blur(10rem);
    transform: scale(0.5);
}

.thinking-animation .blob-4.alt {
    animation-direction: alternate;
    opacity: 0.8;
}

.thinking-text {
    display: none; /* Hide text to match reference design */
    color: #00d4aa;
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    opacity: 0.9;
    transition: color 0.3s ease;
}

/* Subtle color variation when speaking */
.ai-thinking-indicator[data-state="speaking"] .thinking-text {
    color: #74d9e1;
}

.ai-thinking-indicator[data-state="speaking"] .blob-1 path {
    fill: #74d9e1;
}

.ai-thinking-indicator[data-state="speaking"] .blob-3 path {
    fill: #00d4aa;
}

/* Blob Animation Keyframes */
@keyframes blob-anim-1 {
    0% {
        d: path("M 100 600 q 0 -500, 500 -500 t 500 500 t -500 500 T 100 600 z");
    }
    30% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    70% {
        d: path("M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
}

@keyframes blob-anim-2 {
    0% {
        d: path("M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    40% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
    80% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 100 600 q 100 -600, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
}

@keyframes blob-anim-3 {
    0% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    35% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
    75% {
        d: path("M 100 600 q 100 -600, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 100 600 q 0 -400, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
}

@keyframes blob-anim-4 {
    0% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
    30% {
        d: path("M 100 600 q 100 -600, 500 -500 t 400 500 t -500 500 T 100 600 z");
    }
    70% {
        d: path("M 100 600 q -50 -400, 500 -500 t 450 550 t -500 500 T 100 600 z");
    }
    100% {
        d: path("M 150 600 q 0 -600, 500 -500 t 500 550 t -500 500 T 150 600 z");
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Responsive adjustments for thinking animation */
@media (max-width: 480px) {
    .ai-thinking-indicator {
        padding: 20px;
    }

    .thinking-animation {
        width: 100px;
        height: 100px;
    }

    .thinking-text {
        font-size: 1rem;
    }
}

/* History Modal Styles */
.history-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-in-out;
}

.history-modal-content {
    background: #1a1a1a;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #333;
}

.history-header h2 {
    color: #ffffff;
    margin: 0;
    font-size: 1.5rem;
}

.close-history {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-history:hover {
    background: #333;
}

.history-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 25px;
    max-height: 50vh;
}

/* Grouped Conversation Styles */
.conversation-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.conversation-group {
    background: #2a2a2a;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.conversation-group:hover {
    border-color: #00d4aa;
}

.conversation-summary {
    padding: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: background 0.3s ease;
}

.conversation-summary:hover {
    background: #333;
}

.conversation-header {
    flex: 1;
}

.conversation-title {
    color: #ffffff;
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.conversation-meta {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 8px;
}

.conversation-date {
    color: #888;
    font-size: 0.9rem;
}

.message-count {
    color: #00d4aa;
    font-size: 0.85rem;
    background: rgba(0, 212, 170, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
}

.conversation-preview {
    color: #ccc;
    font-size: 0.9rem;
    line-height: 1.4;
}

.expand-icon {
    color: #888;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.conversation-details {
    border-top: 1px solid #333;
    background: #1e1e1e;
}

.message-list {
    padding: 0;
}

.message-exchange {
    padding: 15px 20px;
    border-bottom: 1px solid #333;
}

.message-exchange:last-child {
    border-bottom: none;
}

.message-time {
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.user-message {
    color: #ffffff;
    margin-bottom: 10px;
    padding: 8px 12px;
    background: #333;
    border-radius: 8px;
    border-left: 3px solid #007acc;
}

.ai-message {
    color: #00d4aa;
    padding: 8px 12px;
    background: rgba(0, 212, 170, 0.05);
    border-radius: 8px;
    border-left: 3px solid #00d4aa;
    line-height: 1.4;
}

.conversation-actions {
    padding: 15px 20px;
    border-top: 1px solid #333;
    background: #1a1a1a;
    display: flex;
    justify-content: center;
}

.continue-btn {
    background: #00d4aa;
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.continue-btn:hover {
    background: #00b894;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
}

.continue-btn i {
    font-size: 0.9rem;
}

.system-message {
    background: rgba(136, 136, 136, 0.1);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 15px 0;
    text-align: center;
    color: #888;
    font-style: italic;
}

.history-actions {
    display: flex;
    gap: 15px;
    padding: 20px 25px;
    border-top: 1px solid #333;
    justify-content: flex-end;
}

.export-btn, .clear-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.export-btn {
    background: #00d4aa;
    color: #000;
}

.export-btn:hover {
    background: #00b894;
}

.clear-btn {
    background: #e74c3c;
    color: #fff;
}

.clear-btn:hover {
    background: #c0392b;
}

.loading {
    text-align: center;
    color: #888;
    padding: 40px;
}

.no-history {
    text-align: center;
    color: #888;
    padding: 40px;
}




