require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cookieParser = require('cookie-parser');
const path = require('path');
const fs = require('fs');

// Import services and middleware
const AIService = require('./services/aiService');
const { STOCK_BUSINESS_SYSTEM_PROMPT, CONVERSATION_STARTERS, MARKET_INSIGHTS } = require('./prompts');
const { authService, optionalAuth } = require('./middleware/auth');
const {
  validateMessage,
  validateLeadData,
  validateConversationHistory,
  validateUserRegistration,
  validateUserLogin,
  validateTextToSpeech,
  handleValidationErrors
} = require('./middleware/validation');
const { encryptionService, encryptConversation, decryptConversation, anonymizeForLogging } = require('./services/encryption');
const { upload, additionalSecurityChecks, secureDelete } = require('./middleware/fileUpload');
const { accessControlService, requireAccessPassword, validateAccessPasswordInput } = require('./middleware/accessControl');

// Full context conversation loading system (GPT-4o 128k context)

/**
 * Parse timestamp to Date object for sorting
 */
function parseTimestamp(timestamp) {
  try {
    // Handle different timestamp formats
    if (timestamp.includes('/')) {
      // Format: "03/07/2025, 08:26:35" or "04/08/2025, 12:17:44"
      const [datePart, timePart] = timestamp.split(', ');
      const [day, month, year] = datePart.split('/');
      const [hour, minute, second] = timePart.split(':');
      return new Date(year, month - 1, day, hour, minute, second);
    } else {
      // ISO format or other formats
      return new Date(timestamp);
    }
  } catch (error) {
    console.warn('Could not parse timestamp:', timestamp);
    return new Date(0); // Return epoch if parsing fails
  }
}

/**
 * Load all conversation files and combine them chronologically
 */
function loadAllConversations() {
  const allConversations = [];
  const conversationFiles = [
    './july_collectivechat_data.json',
    './Collective Chat.json',
    './Curation Collective.json'
  ];

  let totalLoaded = 0;

  conversationFiles.forEach(filePath => {
    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      if (Array.isArray(data)) {
        // Add source file info to each conversation
        const conversationsWithSource = data.map(conv => ({
          ...conv,
          sourceFile: filePath.replace('./', '').replace('.json', '')
        }));
        allConversations.push(...conversationsWithSource);
        totalLoaded += data.length;
        console.log(`✅ Loaded ${data.length} conversations from ${filePath}`);
      }
    } catch (error) {
      console.log(`⚠️ Could not load ${filePath}:`, error.message);
    }
  });

  // Sort all conversations chronologically
  allConversations.sort((a, b) => {
    const dateA = parseTimestamp(a.timestamp);
    const dateB = parseTimestamp(b.timestamp);
    return dateA - dateB;
  });

  console.log(`🎯 Total conversations loaded: ${totalLoaded}`);
  console.log(`📅 Date range: ${allConversations[0]?.timestamp} to ${allConversations[allConversations.length - 1]?.timestamp}`);

  return allConversations;
}

// Load all conversations with full context (no limits for GPT-4o 128k)
function loadCleanedConversations() {
  try {
    // Load ALL conversations from all files
    const allConversations = loadAllConversations();

    if (allConversations.length === 0) {
      console.log('⚠️ No conversations loaded');
      return '';
    }

    // Format recent conversations for AI reference
    let formattedConversations = '\n\n## RECENT MARKET CONVERSATION HISTORY\n\n';
    formattedConversations += `🎯 You have access to recent market conversations (showing latest 200 of ${allConversations.length} total conversations).\n`;
    formattedConversations += '📅 Conversations are sorted chronologically from earliest to most recent.\n';
    formattedConversations += '🔍 These represent the most recent and relevant market discussions.\n\n';

    formattedConversations += '### 📊 RECENT CONVERSATION TIMELINE:\n\n';

    // Limit conversations to stay within token limits (use recent 200 conversations)
    const maxConversations = 200;
    const recentConversations = allConversations.slice(-maxConversations);

    // Add recent conversations in chronological order
    recentConversations.forEach((conv) => {
      const sender = conv.sender || 'Unknown';
      const message = conv.message || '';
      const source = conv.sourceFile || 'unknown';

      formattedConversations += `**${conv.timestamp}** [${source}] - ${sender}: "${message}"\n\n`;
    });

    formattedConversations += '\n🔥 INSTRUCTIONS FOR USING THIS COMPLETE CONTEXT:\n';
    formattedConversations += '1. You have access to the ENTIRE conversation history - use it to provide comprehensive answers\n';
    formattedConversations += '2. Reference specific conversations, dates, and participants when relevant\n';
    formattedConversations += '3. You can trace the evolution of discussions over time\n';
    formattedConversations += '4. Provide detailed, well-informed responses using this complete context\n';
    formattedConversations += '5. When asked about trends, you can analyze patterns across the full timeline\n\n';

    const totalChars = formattedConversations.length;
    const estimatedTokens = Math.ceil(totalChars / 4);
    console.log(`✅ Loaded recent conversation history: ${recentConversations.length} of ${allConversations.length} total conversations`);
    console.log(`📊 Total characters: ${totalChars}, Estimated tokens: ${estimatedTokens}`);

    return formattedConversations;
  } catch (error) {
    console.log('Could not load conversations:', error.message);
    return '';
  }
}

// Initialize AI service with fallback support
const aiService = new AIService();

// Conversation history file path
const CONVERSATION_FILE = path.join(__dirname, 'conversation_history.json');

// Conversation history functions
async function saveConversationExchange(userMessage, aiResponse, leadData, userId = null) {
  try {
    const conversationFile = path.join(__dirname, 'data', 'conversation_history.json');
    const dataDir = path.dirname(conversationFile);

    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    let conversations = [];

    // Read existing conversations if file exists
    if (fs.existsSync(conversationFile)) {
      try {
        const data = fs.readFileSync(conversationFile, 'utf8');
        const encryptedConversations = JSON.parse(data);

        // Decrypt existing conversations
        conversations = encryptedConversations.map(conv => {
          try {
            return decryptConversation(conv);
          } catch (error) {
            console.warn('Failed to decrypt conversation:', error.message);
            return null;
          }
        }).filter(conv => conv !== null);
      } catch (error) {
        console.error('Error reading conversation history:', error);
      }
    }

    // Add new conversation exchange
    const exchange = {
      id: encryptionService.generateSecureToken(16),
      timestamp: new Date().toISOString(),
      userMessage,
      aiResponse,
      leadData: leadData || {},
      userId: userId,
      sessionId: Date.now().toString()
    };

    conversations.push(exchange);

    // Keep only last 100 exchanges to prevent file from getting too large
    if (conversations.length > 100) {
      conversations = conversations.slice(-100);
    }

    // Encrypt conversations before saving
    const encryptedConversations = conversations.map(conv => encryptConversation(conv));

    // Save back to file
    fs.writeFileSync(conversationFile, JSON.stringify(encryptedConversations, null, 2));

    console.log('💾 Conversation saved securely');
  } catch (error) {
    console.error('Error saving conversation exchange:', anonymizeForLogging(error));
  }
}

async function getConversationSummary() {
  try {
    const conversationFile = path.join(__dirname, 'conversation_history.json');

    if (!fs.existsSync(conversationFile)) {
      return "I don't have any previous conversation history to summarize yet. This appears to be our first interaction!";
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const conversations = JSON.parse(data);

    if (conversations.length === 0) {
      return "I don't have any previous conversation history to summarize yet.";
    }

    // Get conversations from the last week
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentConversations = conversations.filter(conv =>
      new Date(conv.timestamp) > oneWeekAgo
    );

    if (recentConversations.length === 0) {
      return "I don't have any conversations from the last week to summarize.";
    }

    // Create summary
    const leadInfo = recentConversations
      .map(conv => conv.leadData)
      .filter(data => data && Object.keys(data).length > 0)
      .pop(); // Get the most recent lead data

    const keyTopics = recentConversations
      .map(conv => conv.userMessage)
      .join(' ')
      .toLowerCase();

    let summary = `Here's a summary of our recent conversations:\n\n`;
    summary += `📅 **Time Period**: Last week (${recentConversations.length} exchanges)\n\n`;

    if (leadInfo && Object.keys(leadInfo).length > 0) {
      summary += `👤 **Lead Information Collected**:\n`;
      if (leadInfo.name) summary += `- Name: ${leadInfo.name}\n`;
      if (leadInfo.email) summary += `- Email: ${leadInfo.email}\n`;
      if (leadInfo.phone) summary += `- Phone: ${leadInfo.phone}\n`;
      summary += `\n`;
    }

    summary += `💬 **Key Discussion Topics**:\n`;
    if (keyTopics.includes('invest') || keyTopics.includes('stock')) {
      summary += `- Investment interests and stock market discussions\n`;
    }
    if (keyTopics.includes('portfolio') || keyTopics.includes('manage')) {
      summary += `- Portfolio management and investment strategies\n`;
    }
    if (keyTopics.includes('beginner') || keyTopics.includes('new')) {
      summary += `- Beginner investment guidance and education\n`;
    }
    if (keyTopics.includes('advisor') || keyTopics.includes('consultation')) {
      summary += `- Financial advisor services and consultation scheduling\n`;
    }

    summary += `\n📊 **Next Steps**: Based on our conversations, I'd recommend scheduling a consultation to discuss your investment goals in more detail.`;

    return summary;

  } catch (error) {
    console.error('Error getting conversation summary:', error);
    return "I'm having trouble accessing the conversation history right now, but I'm here to help with any investment questions you have!";
  }
}

async function getPreviousConversationContext(leadData) {
  try {
    const conversationFile = path.join(__dirname, 'conversation_history.json');

    if (!fs.existsSync(conversationFile)) {
      return null;
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const conversations = JSON.parse(data);

    if (conversations.length === 0) {
      return null;
    }

    // Try to find conversations for this specific lead
    let relevantConversations = [];

    if (leadData && (leadData.email || leadData.phone || leadData.name)) {
      // Find conversations with matching lead data
      relevantConversations = conversations.filter(conv => {
        const convLead = conv.leadData || {};
        return (leadData.email && convLead.email === leadData.email) ||
               (leadData.phone && convLead.phone === leadData.phone) ||
               (leadData.name && convLead.name === leadData.name);
      });
    }

    // If no specific lead match, get recent conversations (last 24 hours)
    if (relevantConversations.length === 0) {
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);

      relevantConversations = conversations.filter(conv =>
        new Date(conv.timestamp) > oneDayAgo
      ).slice(-5); // Last 5 exchanges
    }

    if (relevantConversations.length === 0) {
      return null;
    }

    // Create context summary
    let context = "Previous conversation context:\n";

    const lastLead = relevantConversations
      .map(conv => conv.leadData)
      .filter(data => data && Object.keys(data).length > 0)
      .pop();

    if (lastLead && Object.keys(lastLead).length > 0) {
      context += `Lead info: ${JSON.stringify(lastLead)}\n`;
    }

    // Add key conversation points
    const recentExchanges = relevantConversations.slice(-3);
    context += "Recent discussion points:\n";
    recentExchanges.forEach(conv => {
      context += `User: ${conv.userMessage.substring(0, 100)}...\n`;
      context += `You: ${conv.aiResponse.substring(0, 100)}...\n`;
    });

    return context;

  } catch (error) {
    console.error('Error getting previous conversation context:', error);
    return null;
  }
}

const app = express();
const PORT = process.env.PORT || 3000;

// Security Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com", "https://api.anthropic.com"],
      fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "blob:", "data:"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration
const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
  'http://localhost:3000',
  'http://127.0.0.1:3000'
];

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1 || process.env.NODE_ENV === 'development') {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Too many requests from this IP',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Body parsing with limits
app.use(express.json({
  limit: '10mb',
  verify: (req, res, buf) => {
    // Store raw body for signature verification if needed
    req.rawBody = buf;
  }
}));

app.use(express.urlencoded({
  extended: true,
  limit: '10mb'
}));

// Cookie parser for access control
app.use(cookieParser());

// Apply access control to all routes except access-related ones
app.use(requireAccessPassword);

// Static files
app.use(express.static('public', {
  maxAge: process.env.NODE_ENV === 'production' ? '1d' : 0,
  etag: true
}));

// Force HTTPS in production
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
      next();
    }
  });
}

// Secure file upload configuration is imported from middleware/fileUpload.js

// Authentication Routes
app.post('/api/auth/register',
  validateUserRegistration(),
  handleValidationErrors,
  async (req, res) => {
    try {
      const { username, email, password } = req.body;

      const user = await authService.createUser({
        username,
        email,
        password,
        role: 'user'
      });

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        user
      });
    } catch (error) {
      console.error('Registration error:', anonymizeForLogging(error));
      res.status(400).json({
        error: error.message || 'Registration failed',
        timestamp: new Date().toISOString()
      });
    }
  }
);

app.post('/api/auth/login',
  validateUserLogin(),
  handleValidationErrors,
  async (req, res) => {
    try {
      const { username, password } = req.body;

      const authResult = await authService.authenticateUser(username, password);

      if (!authResult) {
        return res.status(401).json({
          error: 'Invalid credentials',
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        success: true,
        message: 'Login successful',
        ...authResult
      });
    } catch (error) {
      console.error('Login error:', anonymizeForLogging(error));
      res.status(500).json({
        error: 'Login failed',
        timestamp: new Date().toISOString()
      });
    }
  }
);

// Access Control Routes (before access protection)
app.post('/api/access/verify',
  validateAccessPasswordInput(),
  handleValidationErrors,
  async (req, res) => {
    try {
      const { password } = req.body;

      const isValid = await accessControlService.validateAccessPassword(password);

      if (!isValid) {
        return res.status(401).json({
          error: 'Invalid access password',
          timestamp: new Date().toISOString()
        });
      }

      // Create access session
      const accessToken = accessControlService.createAccessSession(req);

      // Set secure cookie
      res.cookie('accessToken', accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: parseInt(process.env.ACCESS_SESSION_TIMEOUT) || 24 * 60 * 60 * 1000
      });

      res.json({
        success: true,
        message: 'Access granted',
        expiresIn: parseInt(process.env.ACCESS_SESSION_TIMEOUT) || 24 * 60 * 60 * 1000
      });
    } catch (error) {
      console.error('Access verification error:', anonymizeForLogging(error));
      res.status(500).json({
        error: 'Access verification failed',
        timestamp: new Date().toISOString()
      });
    }
  }
);

app.get('/api/access/status', (req, res) => {
  const accessToken = req.cookies?.accessToken || req.headers['x-access-token'];
  const isValid = accessControlService.isValidAccessSession(accessToken);

  res.json({
    hasAccess: isValid,
    stats: accessControlService.getAccessStats(),
    timestamp: new Date().toISOString()
  });
});

app.post('/api/access/logout', (req, res) => {
  const accessToken = req.cookies?.accessToken || req.headers['x-access-token'];

  if (accessToken) {
    accessControlService.revokeAccessSession(accessToken);
  }

  res.clearCookie('accessToken');
  res.json({
    success: true,
    message: 'Access revoked'
  });
});

// Serve static files
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API Routes
app.post('/api/speech-to-text',
  upload.single('audio'),
  additionalSecurityChecks,
  async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    console.log('Received audio file:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      filename: req.file.filename
    });

    // Determine the correct file extension for OpenAI
    let fileExtension = '.wav'; // Default to WAV
    if (req.file.mimetype.includes('mp4')) {
      fileExtension = '.mp4';
    } else if (req.file.mimetype.includes('mpeg')) {
      fileExtension = '.mp3';
    } else if (req.file.mimetype.includes('ogg')) {
      fileExtension = '.ogg';
    } else if (req.file.mimetype.includes('webm')) {
      fileExtension = '.webm';
    }

    // Create a new filename with proper extension
    const newFilePath = req.file.path + fileExtension;
    fs.renameSync(req.file.path, newFilePath);

    // Create a readable stream from the renamed file
    const audioStream = fs.createReadStream(newFilePath);

    // Use AI service for speech-to-text
    const transcriptionResult = await aiService.speechToText(audioStream, {
      language: 'en'
    });

    // Clean up the renamed file
    fs.unlinkSync(newFilePath);

    res.json({
      transcription: transcriptionResult.text,
      success: true,
      provider: transcriptionResult.provider
    });

  } catch (error) {
    console.error('Speech-to-text error:', error);

    // Clean up file if it exists (check both original and renamed paths)
    if (req.file) {
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      const newFilePath = req.file.path + (req.file.mimetype.includes('webm') ? '.webm' : '.wav');
      if (fs.existsSync(newFilePath)) {
        fs.unlinkSync(newFilePath);
      }
    }

    // Provide user-friendly error messages
    let userMessage = 'Sorry, I couldn\'t process your audio. Please try speaking again or type your message.';
    let statusCode = 500;

    if (error.message?.includes('temporarily unavailable')) {
      userMessage = error.message; // Use the specific message from AI service
      statusCode = 503;
    } else if (error.message?.includes('not configured')) {
      userMessage = 'Voice recognition is temporarily unavailable. Please type your message instead.';
      statusCode = 503;
    }

    res.status(statusCode).json({
      error: userMessage,
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

app.post('/api/chat',
  validateMessage(),
  validateLeadData(),
  validateConversationHistory(),
  handleValidationErrors,
  async (req, res) => {
  try {
    const { message, context, leadData, conversationHistory } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'No message provided' });
    }

    // Check if user is asking about conversation history
    const historyKeywords = ['what was discussed', 'last week', 'previous conversation', 'chat history', 'what did we talk about'];
    const isHistoryRequest = historyKeywords.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase())
    );

    if (isHistoryRequest) {
      // Load and summarize conversation history
      const conversationSummary = await getConversationSummary();
      return res.json({
        response: conversationSummary,
        success: true
      });
    }

    // Build conversation context
    let systemPrompt = STOCK_BUSINESS_SYSTEM_PROMPT;

    // Add complete conversation data for reference (full context loading)
    const cleanedConversations = loadCleanedConversations();
    if (cleanedConversations) {
      systemPrompt += cleanedConversations;
      console.log('✅ Added complete conversation history to system prompt');
      console.log('📝 First 500 chars of conversation data:', cleanedConversations.substring(0, 500));
    } else {
      console.log('❌ No conversation data loaded');
    }

    // Load previous conversation context for this lead
    const previousContext = await getPreviousConversationContext(leadData);
    if (previousContext) {
      systemPrompt += `\n\nPrevious conversation context: ${previousContext}`;
    }

    if (leadData && Object.keys(leadData).length > 0) {
      systemPrompt += `\n\nCurrent lead information: ${JSON.stringify(leadData)}`;
    }

    // Add a random market insight occasionally to provide value
    if (Math.random() < 0.3) {
      const insight = MARKET_INSIGHTS[Math.floor(Math.random() * MARKET_INSIGHTS.length)];
      systemPrompt += `\n\nConsider sharing this insight if relevant: "${insight}"`;
    }



    // Debug: Log final system prompt details
    console.log('🔍 Final system prompt length:', systemPrompt.length);
    console.log('🔍 Contains GR messages:', systemPrompt.includes('GR'));
    console.log('🔍 Contains Valuation:', systemPrompt.includes('Valuation'));
    console.log('🔍 Last 200 chars of system prompt:', systemPrompt.slice(-200));

    // Build messages array with conversation history
    const messages = [{ role: 'system', content: systemPrompt }];

    // Add conversation history if provided
    if (conversationHistory && Array.isArray(conversationHistory)) {
      messages.push(...conversationHistory);
    }

    // Add current user message
    messages.push({ role: 'user', content: message });

    // Use AI service with automatic fallback (increased tokens for detailed responses)
    const aiResult = await aiService.chatCompletion({
      model: 'gpt-4o',
      messages: messages,
      max_tokens: 1000,
      temperature: 0.7,
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    });

    const response = aiResult.response;

    // Save conversation to history
    await saveConversationExchange(message, response, leadData);

    // Include provider information in response for debugging
    const responseData = {
      response,
      success: true,
      provider: aiResult.provider
    };

    // Add fallback information if applicable
    if (aiResult.fallback) {
      responseData.fallback = true;
      responseData.fallbackReason = aiResult.fallbackReason;
      console.log(`🔄 Used fallback provider: ${aiResult.provider}, reason: ${aiResult.fallbackReason}`);
    }

    res.json(responseData);

  } catch (error) {
    console.error('Chat error:', error);

    // Provide user-friendly error messages
    let userMessage = 'I apologize, but I\'m experiencing technical difficulties right now. Please try again in a moment.';
    let statusCode = 500;

    if (error.message?.includes('Both providers failed')) {
      userMessage = 'Our AI services are temporarily unavailable. Please try again later.';
      statusCode = 503;
    } else if (error.message?.includes('No AI providers available')) {
      userMessage = 'AI services are not configured properly. Please contact support.';
      statusCode = 503;
    } else if (error.message?.includes('rate limit')) {
      userMessage = 'We\'re experiencing high demand. Please wait a moment and try again.';
      statusCode = 429;
    }

    res.status(statusCode).json({
      error: userMessage,
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

app.post('/api/text-to-speech',
  validateTextToSpeech(),
  handleValidationErrors,
  async (req, res) => {
  try {
    console.log('🔊 TTS request received:', { textLength: req.body?.text?.length || 0 });
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'No text provided' });
    }

    // Use AI service for text-to-speech
    const speechResult = await aiService.textToSpeech(text, {
      model: 'tts-1',
      voice: 'alloy', // Professional, clear voice
      speed: 1.0
    });

    // Set appropriate headers for audio response
    res.set({
      'Content-Type': 'audio/mpeg',
      'Content-Length': speechResult.buffer.length,
      'Cache-Control': 'no-cache'
    });

    res.send(speechResult.buffer);

  } catch (error) {
    console.error('Text-to-speech error:', error);

    // Provide user-friendly error messages
    let userMessage = 'Sorry, I couldn\'t generate audio for this response. The text is still available above.';
    let statusCode = 500;

    if (error.message?.includes('temporarily unavailable')) {
      userMessage = error.message; // Use the specific message from AI service
      statusCode = 503;
    } else if (error.message?.includes('not configured')) {
      userMessage = 'Voice synthesis is temporarily unavailable. The text response is still available.';
      statusCode = 503;
    }

    res.status(statusCode).json({
      error: userMessage,
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Conversation History endpoints
app.get('/api/conversation-history', (req, res) => {
  try {
    const conversationFile = path.join(__dirname, 'data', 'conversation_history.json');

    if (!fs.existsSync(conversationFile)) {
      return res.json({
        success: true,
        conversations: []
      });
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const encryptedConversations = JSON.parse(data);

    // Decrypt conversations for the user
    const conversations = encryptedConversations.map(conv => {
      try {
        const decrypted = decryptConversation(conv);
        // Filter by user if not admin
        if (req.user.role !== 'admin' && decrypted.userId !== req.user.userId) {
          return null;
        }
        return decrypted;
      } catch (error) {
        console.warn('Failed to decrypt conversation:', error.message);
        return null;
      }
    }).filter(conv => conv !== null);

    res.json({
      success: true,
      conversations: conversations || []
    });
  } catch (error) {
    console.error('Error reading conversation history:', anonymizeForLogging(error));
    res.status(500).json({
      success: false,
      error: 'Failed to load conversation history'
    });
  }
});

app.delete('/api/conversation-history', (req, res) => {
  try {
    if (fs.existsSync(CONVERSATION_FILE)) {
      fs.writeFileSync(CONVERSATION_FILE, JSON.stringify([], null, 2));
    }

    res.json({
      success: true,
      message: 'Conversation history cleared successfully'
    });
  } catch (error) {
    console.error('Error clearing conversation history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear conversation history'
    });
  }
});

// AI Service Status endpoint
app.get('/api/ai-status', (req, res) => {
  try {
    const status = aiService.getStatus();
    res.json({
      success: true,
      ...status
    });
  } catch (error) {
    console.error('AI status error:', error);
    res.status(500).json({
      error: 'Failed to get AI service status',
      details: error.message
    });
  }
});

// AI Health Check endpoint
app.get('/api/ai-health', async (req, res) => {
  try {
    const provider = req.query.provider || 'openai';
    const healthResult = await aiService.healthCheck(provider);

    res.json({
      success: true,
      ...healthResult
    });
  } catch (error) {
    console.error('AI health check error:', error);
    res.status(500).json({
      error: 'Failed to perform health check',
      details: error.message
    });
  }
});

app.post('/api/save-lead', async (req, res) => {
  try {
    const leadData = req.body;

    // Add timestamp
    leadData.timestamp = new Date().toISOString();
    leadData.id = Date.now().toString();

    // In a real application, you would save to a database
    // For now, we'll save to a JSON file
    const leadsFile = path.join(__dirname, 'leads.json');
    let leads = [];

    // Read existing leads if file exists
    if (fs.existsSync(leadsFile)) {
      try {
        const data = fs.readFileSync(leadsFile, 'utf8');
        leads = JSON.parse(data);
      } catch (error) {
        console.error('Error reading leads file:', error);
      }
    }

    // Add new lead
    leads.push(leadData);

    // Save back to file
    fs.writeFileSync(leadsFile, JSON.stringify(leads, null, 2));

    res.json({
      success: true,
      message: 'Lead saved successfully',
      leadId: leadData.id
    });

  } catch (error) {
    console.error('Save lead error:', error);
    res.status(500).json({
      error: 'Failed to save lead',
      details: error.message
    });
  }
});

// Global error handling middleware
app.use((error, req, res, next) => {
  // Log error securely (without sensitive data)
  const errorLog = {
    message: error.message,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    timestamp: new Date().toISOString(),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    url: req.url,
    method: req.method,
    userId: req.user?.userId || 'anonymous'
  };

  console.error('Application error:', anonymizeForLogging(errorLog));

  // Send generic error response
  const statusCode = error.status || error.statusCode || 500;
  const message = statusCode === 500 ? 'Internal server error' : error.message;

  res.status(statusCode).json({
    error: message,
    timestamp: new Date().toISOString(),
    requestId: encryptionService.generateSecureToken(8)
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    timestamp: new Date().toISOString()
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  const os = require('os');
  const networkInterfaces = os.networkInterfaces();
  let networkIP = 'localhost';

  // Find the first non-internal IPv4 address
  for (const interfaceName in networkInterfaces) {
    const addresses = networkInterfaces[interfaceName];
    for (const address of addresses) {
      if (address.family === 'IPv4' && !address.internal) {
        networkIP = address.address;
        break;
      }
    }
    if (networkIP !== 'localhost') break;
  }

  console.log('🚀 Echo Voice Leads server starting...');
  console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔐 Security: ${process.env.NODE_ENV === 'production' ? 'PRODUCTION' : 'DEVELOPMENT'} mode`);
  console.log(`🌐 Server running on port ${PORT}`);
  console.log(`📱 Local:   http://localhost:${PORT}`);

  if (process.env.NODE_ENV !== 'production') {
    console.log(`🌍 Network: http://${networkIP}:${PORT}`);
    console.log('⚠️  Access from other devices on the same WiFi using the Network URL');
    console.log('⚠️  Default admin credentials: admin/admin123 (CHANGE IMMEDIATELY!)');
  }

  console.log('✅ Server ready for connections');
});

// Handle server errors
server.on('error', (error) => {
  console.error('Server error:', error);
  process.exit(1);
});
